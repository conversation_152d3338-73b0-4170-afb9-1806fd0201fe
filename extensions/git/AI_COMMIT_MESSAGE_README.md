# AI Commit Message Generation

This feature provides intelligent commit message generation using AI services.

## Features

1. **Automatic Generation**: When committing with an empty message, AI will automatically generate a commit message based on staged changes
2. **Manual Generation**: Use the "Generate Commit Message with AI" command to manually generate messages
3. **Input Box Integration**: AI generation button in the SCM input box toolbar
4. **Commit Menu Integration**: AI generation option in the commit button dropdown menu

## Configuration

### Settings

- `git.enableAICommitMessage` (boolean, default: true): Enable/disable AI commit message generation
- `git.aiCommitMessageProvider` (string, default: "openai"): AI service provider (openai/local/custom)
- `git.aiCommitMessageMaxLength` (number, default: 72): Maximum length of generated commit messages

### Usage

1. **Automatic Generation**: 
   - Stage your changes
   - Click commit without entering a message
   - AI will automatically generate and fill the commit message

2. **Manual Generation**:
   - Use Command Palette: "Git: Generate Commit Message with AI"
   - Click the sparkle icon in the SCM input box
   - Select from the commit button dropdown menu

## Implementation for Developers

The AI service is implemented as a placeholder function that you need to customize:

### Location
`extensions/git/src/aiCommitMessageService.ts` - `callAIService` method

### Example Implementation
```typescript
private async callAIService(diffInfo: DiffInfo): Promise<string> {
    const prompt = this.buildPrompt(diffInfo);
    
    // Replace this with your AI service call
    const response = await fetch('your-ai-api-endpoint', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt })
    });
    
    return response.text();
}
```

### Available Data
The `diffInfo` parameter contains:
- `stagedFiles`: Array of file changes with path and status
- `diffContent`: Text summary of changes
- `repositoryInfo`: Repository name, branch, and root path

## Commands

- `git.generateCommitMessage`: Generate commit message with AI

## Menu Locations

- Command Palette: "Git: Generate Commit Message with AI"
- SCM Input Box: Sparkle icon button
- Commit Button Dropdown: "Generate Commit Message with AI" option
