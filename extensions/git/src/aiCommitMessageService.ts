import { LogOutputChannel, workspace, Uri } from 'vscode';
import TelemetryReporter from '@vscode/extension-telemetry';
import { Repository } from './repository';

export interface DiffInfo {
	stagedFiles: FileChange[];
	diffContent: string;
	repositoryInfo: RepositoryInfo;
}

export interface FileChange {
	path: string;
	status: 'added' | 'modified' | 'deleted' | 'renamed';
	oldPath?: string;
}

export interface RepositoryInfo {
	name: string;
	branch: string;
	root: string;
}

export interface IAICommitMessageService {
	generateCommitMessage(repository: Repository): Promise<string | undefined>;
}

export class AICommitMessageService implements IAICommitMessageService {
	constructor(
		private readonly logger: LogOutputChannel,
		private readonly telemetryReporter: TelemetryReporter
	) {}

	async generateCommitMessage(repository: Repository): Promise<string | undefined> {
		try {
			this.logger.info('[AI Commit] Starting AI commit message generation...');

			// 1. 检查是否启用AI功能
			const config = workspace.getConfiguration('git', Uri.file(repository.root));
			const isEnabled = config.get<boolean>('enableAICommitMessage', true);
			this.logger.info(`[AI Commit] AI feature enabled: ${isEnabled}`);

			if (!isEnabled) {
				return undefined;
			}

			// 2. 收集git diff信息
			const diffInfo = await this.collectDiffInfo(repository);
			if (!diffInfo || diffInfo.stagedFiles.length === 0) {
				this.logger.info('[AI Commit] No staged changes found');
				return undefined;
			}

			this.logger.info(`[AI Commit] Found ${diffInfo.stagedFiles.length} staged files`);

			// 3. 调用AI接口生成commit message
			const generatedMessage = await this.callAIService(diffInfo);
			if (!generatedMessage) {
				this.logger.warn('[AI Commit] AI service returned empty message');
				return undefined;
			}

			// 4. 后处理和验证
			const processedMessage = this.postProcessMessage(generatedMessage, config);

			this.logger.info(`[AI Commit] Generated message: ${processedMessage}`);
			return processedMessage;

		} catch (error) {
			this.logger.error(`[AI Commit] Failed to generate commit message: ${error}`);
			return undefined;
		}
	}

	private async collectDiffInfo(repository: Repository): Promise<DiffInfo | undefined> {
		try {
			// 获取staged changes
			const stagedChanges = repository.indexGroup.resourceStates;
			if (stagedChanges.length === 0) {
				return undefined;
			}

			// 构建文件变更信息
			const stagedFiles: FileChange[] = stagedChanges.map(change => ({
				path: change.resourceUri.fsPath.replace(repository.root + '/', ''),
				status: this.mapStatusToChangeType(change.type),
				oldPath: change.renameResourceUri?.fsPath.replace(repository.root + '/', '')
			}));

			// 获取diff内容 (简化版本，实际可以调用git diff --staged)
			const diffContent = await this.getStagedDiff(repository);

			// 构建仓库信息
			const repositoryInfo: RepositoryInfo = {
				name: repository.root.split('/').pop() || 'unknown',
				branch: repository.headShortName || 'unknown',
				root: repository.root
			};

			return {
				stagedFiles,
				diffContent,
				repositoryInfo
			};

		} catch (error) {
			this.logger.error(`[AI Commit] Failed to collect diff info: ${error}`);
			return undefined;
		}
	}

	private async getStagedDiff(repository: Repository): Promise<string> {
		try {
			// 这里应该调用git diff --staged命令获取详细的diff内容
			// 为了简化，我们先返回文件列表的摘要
			const stagedChanges = repository.indexGroup.resourceStates;
			const summary = stagedChanges.map(change => {
				const relativePath = change.resourceUri.fsPath.replace(repository.root + '/', '');
				return `${this.mapStatusToChangeType(change.type)}: ${relativePath}`;
			}).join('\n');

			return summary;
		} catch (error) {
			this.logger.error(`[AI Commit] Failed to get staged diff: ${error}`);
			return '';
		}
	}

	private mapStatusToChangeType(status: number): 'added' | 'modified' | 'deleted' | 'renamed' {
		// 这里需要根据VSCode Git扩展的状态码映射
		// 简化版本的映射
		switch (status) {
			case 0: return 'added';
			case 1: return 'modified';
			case 2: return 'deleted';
			case 3: return 'renamed';
			default: return 'modified';
		}
	}

	private async callAIService(diffInfo: DiffInfo): Promise<string> {
		// TODO: 这里是AI服务调用的空函数，由用户后续实现
		// 函数签名和基本结构已经准备好，用户可以在这里调用自己的AI模型API

		

		// 用户需要在这里实现AI调用逻辑
		// 例如：
		// const prompt = this.buildPrompt(diffInfo);
		// const response = await fetch('your-ai-api-endpoint', {
		//     method: 'POST',
		//     headers: { 'Content-Type': 'application/json' },
		//     body: JSON.stringify({ prompt })
		// });
		// return response.text();

		// 为了避免编译警告，这里引用buildPrompt方法
		// 用户在实现时可以取消注释上面的代码
		void this.buildPrompt;

		// 记录遥测数据
		this.telemetryReporter.sendTelemetryEvent('ai.commitMessage.generated', {
			fileCount: diffInfo.stagedFiles.length.toString(),
			provider: 'placeholder'
		});

		// 临时返回一个示例消息，用户需要替换为实际的AI调用
		return `feat: update ${diffInfo.stagedFiles.length} file(s)`;
	}

	/**
	 * Helper method to build AI prompt from diff information.
	 * This method is provided for user convenience when implementing AI service calls.
	 */
	private buildPrompt(diffInfo: DiffInfo): string {
		const { stagedFiles, repositoryInfo } = diffInfo;

		let prompt = `Generate a concise commit message for the following changes in repository "${repositoryInfo.name}" on branch "${repositoryInfo.branch}":\n\n`;

		prompt += 'Changed files:\n';
		stagedFiles.forEach(file => {
			prompt += `- ${file.status}: ${file.path}\n`;
		});

		prompt += '\nPlease generate a commit message following conventional commit format (type: description).';
		prompt += '\nKeep it under 72 characters and be specific about what was changed.';

		return prompt;
	}

	private postProcessMessage(message: string, config: any): string {
		if (!message) {
			return '';
		}

		// 获取最大长度配置
		const maxLength = config.get('aiCommitMessageMaxLength', 72) as number;

		// 清理和格式化消息
		let processedMessage = message.trim();

		// 移除多余的引号
		if (processedMessage.startsWith('"') && processedMessage.endsWith('"')) {
			processedMessage = processedMessage.slice(1, -1);
		}

		// 限制长度
		if (processedMessage.length > maxLength) {
			processedMessage = processedMessage.substring(0, maxLength - 3) + '...';
		}

		// 确保首字母小写（conventional commit格式）
		if (processedMessage.length > 0) {
			const colonIndex = processedMessage.indexOf(':');
			if (colonIndex > 0 && colonIndex < processedMessage.length - 1) {
				const type = processedMessage.substring(0, colonIndex);
				const description = processedMessage.substring(colonIndex + 1).trim();
				if (description.length > 0) {
					processedMessage = type + ': ' + description.charAt(0).toLowerCase() + description.slice(1);
				}
			}
		}

		return processedMessage;
	}
}
