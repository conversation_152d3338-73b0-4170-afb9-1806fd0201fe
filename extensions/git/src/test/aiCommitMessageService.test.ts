/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as assert from 'assert';
import { AICommitMessageService } from '../aiCommitMessageService';
import { LogOutputChannel } from 'vscode';
import TelemetryReporter from '@vscode/extension-telemetry';

suite('AICommitMessageService', () => {
	let service: AICommitMessageService;
	let mockLogger: LogOutputChannel;
	let mockTelemetryReporter: TelemetryReporter;

	setup(() => {
		mockLogger = {
			info: () => {},
			error: () => {},
			warn: () => {}
		} as any;

		mockTelemetryReporter = {
			sendTelemetryEvent: () => {}
		} as any;

		service = new AICommitMessageService(mockLogger, mockTelemetryReporter);
	});

	test('should create service instance', () => {
		assert.ok(service);
	});

	test('should have generateCommitMessage method', () => {
		assert.ok(typeof service.generateCommitMessage === 'function');
	});

	// Note: More comprehensive tests would require mocking the Repository class
	// and setting up a proper test environment with staged changes
});
