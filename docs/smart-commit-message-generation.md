# 智能生成Commit Message技术方案

## 1. 需求概述

在IDE的Source Control面板中，当用户点击"Commit"按钮时，如果commit message输入框为空，则自动触发AI智能生成commit message功能。

## 2. 技术架构

### 2.1 VSCode Git架构说明
VSCode的git功能采用分层架构：
```
VSCode核心SCM服务 ←→ SCM扩展API ←→ Git扩展 ←→ Git命令行
```

- **VSCode核心**: 提供通用的SCM框架和UI (`src/vs/workbench/contrib/scm/`)
- **Git扩展**: 作为内置扩展实现git具体功能 (`extensions/git/`)
- **我们的改动**: 在Git扩展层添加AI功能，不涉及VSCode核心代码

### 2.2 整体流程
```
用户点击Commit按钮 → Git扩展处理 → 检查输入框是否为空 → 如果为空则调用AI生成 → 填充到输入框 → 用户确认后提交
```

### 2.3 核心组件

#### 2.3.1 触发点改造
- **位置**: `extensions/git/src/commands.ts` 中的 `commitWithAnyInput` 方法
- **层级**: Git扩展层，不是VSCode核心代码
- **改造点**: 在检查commit message为空时，增加AI生成逻辑
- **原有逻辑**: 当message为空时，弹出输入框让用户手动输入
- **新增逻辑**: 当message为空时，先尝试AI生成，生成失败再fallback到原有逻辑

#### 2.3.2 AI服务接口
- **服务位置**: 新建 `extensions/git/src/aiCommitMessageService.ts`
- **层级**: Git扩展层，作为扩展的一部分
- **职责**:
  - 收集git diff信息
  - 调用AI接口生成commit message
  - 处理生成结果和错误情况

#### 2.3.3 配置管理
- **配置项**:
  - `git.enableAICommitMessage`: 是否启用AI生成功能（默认true）
  - `git.aiCommitMessageProvider`: AI服务提供商配置
  - `git.aiCommitMessageMaxLength`: 生成message的最大长度（默认72）

## 3. 详细实现方案

### 3.1 修改Commit流程

**文件**: `extensions/git/src/commands.ts`

在 `commitWithAnyInput` 方法中的 `getCommitMessage` 函数里添加AI生成逻辑：

```typescript
const getCommitMessage = async () => {
    let _message: string | undefined = message;

    if (!_message && !config.get<boolean>('useEditorAsCommitInput')) {
        // 新增：尝试AI生成commit message
        if (config.get<boolean>('git.enableAICommitMessage', true)) {
            try {
                _message = await this.aiCommitMessageService.generateCommitMessage(repository);
                if (_message) {
                    // 将生成的message填充到inputBox
                    repository.inputBox.value = _message;
                    return _message;
                }
            } catch (error) {
                // AI生成失败，fallback到原有逻辑
                this.logger.warn('AI commit message generation failed:', error);
            }
        }

        // 原有的手动输入逻辑
        const value: string | undefined = undefined;
        // ... 原有代码
    }

    return _message;
};
```

### 3.2 AI服务实现

**文件**: `extensions/git/src/aiCommitMessageService.ts`

```typescript
export interface IAICommitMessageService {
    generateCommitMessage(repository: Repository): Promise<string | undefined>;
}

export class AICommitMessageService implements IAICommitMessageService {
    constructor(
        private readonly logger: LogOutputChannel,
        private readonly telemetryReporter: TelemetryReporter
    ) {}

    async generateCommitMessage(repository: Repository): Promise<string | undefined> {
        // 1. 收集git diff信息
        const diffInfo = await this.collectDiffInfo(repository);

        // 2. 调用AI接口
        const generatedMessage = await this.callAIService(diffInfo);

        // 3. 后处理和验证
        return this.postProcessMessage(generatedMessage);
    }

    private async collectDiffInfo(repository: Repository): Promise<DiffInfo> {
        // 收集staged changes的diff信息
        // 包括：文件列表、修改类型、关键代码变更等
    }

    private async callAIService(diffInfo: DiffInfo): Promise<string> {
        // AI服务调用框架 - 具体实现待定
        // 可以是本地模型、云端API等
    }

    private postProcessMessage(message: string): string {
        // 消息后处理：长度限制、格式规范等
    }
}
```

### 3.3 依赖注入和服务注册

**文件**: `extensions/git/src/commands.ts` (构造函数修改)

```typescript
export class CommandCenter {
    private aiCommitMessageService: IAICommitMessageService;

    constructor(
        private git: Git,
        private model: Model,
        private globalState: Memento,
        private logger: LogOutputChannel,
        private telemetryReporter: TelemetryReporter
    ) {
        // 初始化AI服务
        this.aiCommitMessageService = new AICommitMessageService(
            this.logger,
            this.telemetryReporter
        );

        // 原有初始化代码...
    }
}
```

## 4. 用户体验设计

### 4.1 交互流程
1. 用户点击Commit按钮
2. 如果输入框为空，显示"正在生成commit message..."的loading状态
3. AI生成完成后，自动填充到输入框
4. 用户可以编辑生成的message或直接提交

### 4.2 错误处理
- AI服务不可用：fallback到原有的手动输入流程
- 生成超时：显示超时提示，fallback到手动输入
- 生成内容不合适：用户可以手动修改或重新生成

### 4.3 配置选项
- 用户可以在设置中关闭AI生成功能
- 支持配置不同的AI服务提供商
- 支持自定义生成规则和模板

## 5. AI服务框架设计

### 5.1 抽象接口
```typescript
interface IAIProvider {
    generateCommitMessage(context: CommitContext): Promise<string>;
    isAvailable(): Promise<boolean>;
}

interface CommitContext {
    stagedFiles: FileChange[];
    diffContent: string;
    repositoryInfo: RepositoryInfo;
    userPreferences: UserPreferences;
}
```

### 5.2 支持的AI提供商
- 本地模型（如果有）
- OpenAI GPT
- 其他云端AI服务
- 可扩展的插件机制

### 5.3 Prompt工程
- 基于diff内容生成结构化的prompt
- 包含代码变更类型、影响范围等信息
- 支持多语言commit message生成

## 6. 配置项定义

**文件**: `extensions/git/package.json` (configuration部分)

```json
{
    "git.enableAICommitMessage": {
        "type": "boolean",
        "default": true,
        "description": "Enable AI-powered commit message generation"
    },
    "git.aiCommitMessageProvider": {
        "type": "string",
        "enum": ["openai", "local", "custom"],
        "default": "openai",
        "description": "AI service provider for commit message generation"
    },
    "git.aiCommitMessageMaxLength": {
        "type": "number",
        "default": 72,
        "description": "Maximum length of generated commit message"
    }
}
```

## 7. 实施计划

### Phase 1: 基础框架
- [ ] 创建AI服务接口和基础实现
- [ ] 修改commit流程，添加AI生成触发点
- [ ] 添加基本的配置项

### Phase 2: AI集成
- [ ] 实现具体的AI服务调用
- [ ] 完善diff信息收集逻辑
- [ ] 添加错误处理和fallback机制

### Phase 3: 用户体验优化
- [ ] 添加loading状态和用户反馈
- [ ] 实现重新生成功能
- [ ] 添加用户偏好设置

### Phase 4: 扩展和优化
- [ ] 支持多种AI提供商
- [ ] 优化prompt工程
- [ ] 添加遥测和分析

## 8. 风险和注意事项

### 8.1 技术风险
- AI服务的可用性和稳定性
- 网络延迟对用户体验的影响
- 生成内容的质量控制

### 8.2 用户体验风险
- 过度依赖AI可能降低用户的commit message质量意识
- 需要提供足够的控制权给用户

### 8.3 隐私和安全
- 代码diff信息的隐私保护
- AI服务的数据处理政策
- 本地处理vs云端处理的权衡

## 9. 替代UI方案：添加独立按钮

如果需要在UI上添加独立的"AI生成"按钮，有两个位置可选：

### 9.1 位置1：输入框工具栏（推荐）
**实现难度**: ⭐⭐ (简单)
**位置**: 输入框右侧的工具栏区域
**实现方式**:
```typescript
// 在 extensions/git/package.json 中注册菜单项
"menus": {
  "scm/inputBox": [
    {
      "command": "git.generateCommitMessage",
      "when": "scmProvider == git",
      "group": "navigation"
    }
  ]
}

// 在 extensions/git/src/commands.ts 中注册命令
@command('git.generateCommitMessage', { repository: true })
async generateCommitMessage(repository: Repository): Promise<void> {
    const message = await this.aiCommitMessageService.generateCommitMessage(repository);
    if (message) {
        repository.inputBox.value = message;
    }
}
```

### 9.2 位置2：Commit按钮下拉菜单
**实现难度**: ⭐⭐⭐ (中等)
**位置**: Commit按钮的下拉菜单中
**实现方式**: 修改 `postCommitCommands.ts` 中的 `getCommitActionButtonSecondaryCommands` 方法

### 9.3 对原方案的影响
- **核心AI服务**: 完全不变，仍然是 `aiCommitMessageService.ts`
- **触发方式**: 从"自动触发"改为"手动触发"
- **新增代码**: 只需要添加菜单注册和命令处理，约20-30行代码
- **配置项**: 可以保留，用于控制按钮显示

## 10. 测试策略

### 10.1 单元测试
- AI服务接口的mock测试
- diff信息收集的准确性测试
- 消息后处理逻辑测试

### 10.2 集成测试
- 完整的commit流程测试
- 错误场景的fallback测试
- 不同配置下的行为测试

### 10.3 用户测试
- 真实场景下的可用性测试
- 生成质量的主观评估
- 性能和响应时间测试
